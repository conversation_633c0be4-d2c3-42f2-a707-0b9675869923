"""
测试 basic_sine.init_sampling_info 函数
"""

import pytest
from sweeper400.analyze.basic_sine import init_sampling_info
from sweeper400.analyze.my_dtypes import PositiveInt, SamplingInfo, validate_positive


def test_init_sampling_info_basic():
    """测试基本功能"""
    sampling_rate = PositiveInt(1000)
    samples_num = PositiveInt(2048)
    
    result = init_sampling_info(sampling_rate, samples_num)
    
    # 检查返回类型
    assert isinstance(result, dict)
    
    # 检查字典内容
    assert result["sampling_rate"] == 1000
    assert result["samples_num"] == 2048
    
    # 检查字典键
    assert set(result.keys()) == {"sampling_rate", "samples_num"}


def test_init_sampling_info_different_values():
    """测试不同的输入值"""
    test_cases = [
        (PositiveInt(44100), PositiveInt(1024)),
        (PositiveInt(48000), PositiveInt(4096)),
        (PositiveInt(96000), PositiveInt(8192)),
    ]
    
    for sampling_rate, samples_num in test_cases:
        result = init_sampling_info(sampling_rate, samples_num)
        
        assert result["sampling_rate"] == sampling_rate
        assert result["samples_num"] == samples_num


def test_init_sampling_info_with_validate_positive():
    """测试与 validate_positive 函数配合使用"""
    # 使用 validate_positive 创建 PositiveInt
    sampling_rate = validate_positive(22050)
    samples_num = validate_positive(512)
    
    result = init_sampling_info(sampling_rate, samples_num)
    
    assert result["sampling_rate"] == 22050
    assert result["samples_num"] == 512


def test_validate_positive_error():
    """测试 validate_positive 对非正整数的错误处理"""
    with pytest.raises(ValueError, match="PositiveInt必须是正整数"):
        validate_positive(0)
    
    with pytest.raises(ValueError, match="PositiveInt必须是正整数"):
        validate_positive(-1)


if __name__ == "__main__":
    # 简单的手动测试
    print("运行基本测试...")
    
    # 测试基本功能
    sampling_rate = PositiveInt(1000)
    samples_num = PositiveInt(2048)
    result = init_sampling_info(sampling_rate, samples_num)
    print(f"结果: {result}")
    
    # 测试与 validate_positive 配合
    sampling_rate2 = validate_positive(44100)
    samples_num2 = validate_positive(1024)
    result2 = init_sampling_info(sampling_rate2, samples_num2)
    print(f"结果2: {result2}")
    
    print("所有测试通过！")
